using MetroFramework.Components;
using MetroFramework.Design;
using MetroFramework.Drawing;
using MetroFramework.Interfaces;
using MetroFramework.Native;
using OCRTools;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.Security;
using System.Security.Permissions;
using System.Windows.Forms;
using System.Windows.Forms.Design;

namespace MetroFramework.Controls
{
    [ToolboxBitmap(typeof(TabControl))]
    [Designer(typeof(MetroTabControlDesigner), typeof(ParentControlDesigner))]
    public class MetroTabControl : TabControl, IMetroControl
    {
        private readonly List<string> _tabDisable = new List<string>();

        private bool _bUpDown;

        private MetroColorStyle _metroStyle;

        private MetroThemeStyle _metroTheme;

        private SubClass _scUpDown;

        public MetroTabControl()
        {
            SetStyle(
                ControlStyles.UserPaint |
                ControlStyles.ResizeRedraw |
                ControlStyles.SupportsTransparentBackColor |
                ControlStyles.AllPaintingInWmPaint |
                ControlStyles.OptimizedDoubleBuffer |
                ControlStyles.DoubleBuffer, true);

            SetStyle(ControlStyles.Opaque, false);

            Padding = new Point(6, 8);
            Selecting += MetroTabControl_Selecting;
        }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomBackColor { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseCustomForeColor { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(false)]
        public bool UseStyleColors { get; set; }

        [DefaultValue(false)]
        [Category("Metro Behaviour")]
        [Browsable(false)]
        public bool UseSelectable
        {
            get => GetStyle(ControlStyles.Selectable);
            set => SetStyle(ControlStyles.Selectable, value);
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroTabControlSize.Medium)]
        public MetroTabControlSize FontSize { get; set; } = MetroTabControlSize.Medium;

        [DefaultValue(MetroTabControlWeight.Light)]
        [Category("Metro Appearance")]
        public MetroTabControlWeight FontWeight { get; set; }

        [Category("Metro Appearance")]
        [DefaultValue(ContentAlignment.MiddleLeft)]
        public ContentAlignment TextAlign { get; set; } = ContentAlignment.MiddleLeft;

        [Editor(
            "MetroFramework.Design.MetroTabPageCollectionEditor, MetroFramework.Design, Version=1.4.0.0, Culture=neutral, PublicKeyToken=5f91a84759bf584a",
            typeof(UITypeEditor))]
        public new TabPageCollection TabPages => base.TabPages;

        protected override CreateParams CreateParams
        {
            [SecurityPermission(SecurityAction.LinkDemand, Flags = SecurityPermissionFlag.UnmanagedCode)]
            get
            {
                return base.CreateParams;
            }
        }

        [DefaultValue(MetroCommonStyle.DefaultStyle)]
        [Category("Metro Appearance")]
        public MetroColorStyle Style
        {
            get
            {
                if (DesignMode || _metroStyle != 0) return _metroStyle;
                if (StyleManager != null) return StyleManager.Style;
                if (StyleManager == null) return MetroCommonStyle.DefaultStyle;
                return _metroStyle;
            }
            set => _metroStyle = value;
        }

        [Category("Metro Appearance")]
        [DefaultValue(MetroThemeStyle.Light)]
        public MetroThemeStyle Theme
        {
            get
            {
                if (DesignMode || _metroTheme != 0) return _metroTheme;
                if (StyleManager != null) return StyleManager.Theme;
                if (StyleManager == null) return MetroThemeStyle.Light;
                return _metroTheme;
            }
            set => _metroTheme = value;
        }

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Browsable(false)]
        public MetroStyleManager StyleManager { get; set; }

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintBackground;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaint;

        [Category("Metro Appearance")] public event EventHandler<MetroPaintEventArgs> CustomPaintForeground;

        protected virtual void OnCustomPaintBackground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintBackground != null) CustomPaintBackground(this, e);
        }

        protected virtual void OnCustomPaint(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaint != null) CustomPaint(this, e);
        }

        protected virtual void OnCustomPaintForeground(MetroPaintEventArgs e)
        {
            if (GetStyle(ControlStyles.UserPaint) && CustomPaintForeground != null) CustomPaintForeground(this, e);
        }

        protected override void OnPaintBackground(PaintEventArgs e)
        {
            try
            {
                var color = BackColor;
                if (!UseCustomBackColor) color = MetroPaint.BackColor.Form(Theme);
                if (color.A == byte.MaxValue && BackgroundImage == null)
                {
                    e.Graphics.Clear(color);
                }
                else
                {
                    base.OnPaintBackground(e);
                    OnCustomPaintBackground(new MetroPaintEventArgs(color, Color.Empty, e.Graphics));
                }
            }
            catch
            {
                Invalidate();
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            if (!Size.IsValidate())
                return;
            try
            {
                if (GetStyle(ControlStyles.AllPaintingInWmPaint)) OnPaintBackground(e);
                OnCustomPaint(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
                OnPaintForeground(e);
            }
            catch
            {
                Invalidate();
            }
        }

        protected virtual void OnPaintForeground(PaintEventArgs e)
        {
            for (var i = 0; i < TabPages.Count; i++)
                if (i != SelectedIndex)
                    DrawTab(i, e.Graphics);
            if (SelectedIndex > -1)
            {
                DrawTabBottomBorder(SelectedIndex, e.Graphics);
                DrawTab(SelectedIndex, e.Graphics);
                DrawTabSelected(SelectedIndex, e.Graphics);
                OnCustomPaintForeground(new MetroPaintEventArgs(Color.Empty, Color.Empty, e.Graphics));
            }
        }

        private void DrawTabBottomBorder(int index, Graphics graphics)
        {
            using (Brush brush = new SolidBrush(MetroPaint.BorderColor.TabControl.Normal(Theme)))
            {
                var rect = new Rectangle(DisplayRectangle.X, GetTabRect(index).Bottom + 2 - 3, DisplayRectangle.Width, 3);
                graphics.FillRectangle(brush, rect);
            }
        }

        private void DrawTabSelected(int index, Graphics graphics)
        {
            using (Brush brush = new SolidBrush(MetroPaint.GetStyleColor(Style)))
            {
                var tabRect = GetTabRect(index);
                var rect = new Rectangle(tabRect.X, GetTabRect(index).Bottom + 2 - 3, tabRect.Width, 3);
                graphics.FillRectangle(brush, rect);
            }
        }

        private void DrawTab(int index, Graphics graphics)
        {
            var color = BackColor;
            if (!UseCustomBackColor) color = MetroPaint.BackColor.Form(Theme);
            var tabPage = TabPages[index];
            var tabRect = GetTabRect(index);
            var foreColor = !Enabled || _tabDisable.Contains(tabPage.Name)
                ? MetroPaint.ForeColor.Label.Disabled(Theme)
                : !UseCustomForeColor
                    ? !UseStyleColors ? MetroPaint.ForeColor.TabControl.Normal(Theme) :
                    MetroPaint.GetStyleColor(Style)
                    : DefaultForeColor;
            if (index == 0) tabRect.X = DisplayRectangle.X;
            var rect = tabRect;
            tabRect.Width += 20;
            using (Brush brush = new SolidBrush(color))
            {
                graphics.FillRectangle(brush, rect);
            }

            TextRenderer.DrawText(graphics, tabPage.Text, MetroFonts.TabControl(FontSize, FontWeight), tabRect,
                foreColor, color, MetroPaint.GetTextFormatFlags(TextAlign));
        }

        [SecuritySafeCritical]
        private void DrawUpDown(Graphics graphics)
        {
            var color = Parent?.BackColor ?? MetroPaint.BackColor.Form(Theme);
            var rect = default(Rectangle);
            WinApi.GetClientRect(_scUpDown.Handle, ref rect);
            graphics.CompositingQuality = CompositingQuality.HighQuality;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.Clear(color);
            using (var graphicsPath = new GraphicsPath(FillMode.Winding))
            {
                var num = 10;
                var num2 = 3;
                var num3 = 1.6;
                var num4 = 2.0;
                Brush brush = new SolidBrush(SelectedIndex == 0 ? Color.FromArgb(204, 204, 204) : Color.Gray);
                var points = new[]
                {
                    new PointF(num2, num),
                    new PointF((int) (num * num3) + num2, 0f),
                    new PointF((int) (num * num3) + num2, (int) (num * num4))
                };
                graphicsPath.AddLines(points);
                graphics.FillPath(brush, graphicsPath);
                graphicsPath.Reset();
                brush = new SolidBrush(SelectedIndex == TabCount - 1 ? Color.FromArgb(204, 204, 204) : Color.Gray);
                var points2 = new[]
                {
                    new PointF(rect.Width - (int) (num * num3) - num2, 0f),
                    new PointF(rect.Width - num2, num),
                    new PointF(rect.Width - (int) (num * num3) - num2, (int) (num * num4))
                };
                graphicsPath.AddLines(points2);
                graphics.FillPath(brush, graphicsPath);
                brush.Dispose();
            }
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            Invalidate();
        }

        protected override void OnParentBackColorChanged(EventArgs e)
        {
            base.OnParentBackColorChanged(e);
            Invalidate();
        }

        protected override void OnResize(EventArgs e)
        {
            Log.WriteInfo($"[MetroTabControl] OnResize开始 - Size:{Size}, _bUpDown:{_bUpDown}");
            base.OnResize(e);
            Invalidate();

            // Resize时可能会创建或销毁UpDown控件
            if (!DesignMode)
            {
                Log.WriteInfo($"[MetroTabControl] OnResize中延迟调用FindUpDown");
                BeginInvoke(new Action(() =>
                {
                    Log.WriteInfo($"[MetroTabControl] OnResize延迟调用FindUpDown执行");
                    FindUpDown();
                }));
            }
        }

        [SecuritySafeCritical]
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            if (!DesignMode)
            {
                WinApi.ShowScrollBar(Handle, 3, 0);

                // 监听可能导致UpDown控件创建的消息
                if (m.Msg == 0x0005 || // WM_SIZE
                    m.Msg == 0x0014 || // WM_ERASEBKGND
                    m.Msg == 0x000F || // WM_PAINT
                    m.Msg == 0x0047)   // WM_WINDOWPOSCHANGED
                {
                    var msgName = m.Msg == 0x0005 ? "WM_SIZE" :
                                  m.Msg == 0x0014 ? "WM_ERASEBKGND" :
                                  m.Msg == 0x000F ? "WM_PAINT" : "WM_WINDOWPOSCHANGED";

                    // 延迟查找UpDown控件，确保控件已创建
                    BeginInvoke(new Action(() =>
                    {
                        if (!_bUpDown)
                        {
                            Log.WriteInfo($"[MetroTabControl] 收到{msgName}消息，延迟调用FindUpDown");
                            FindUpDown();
                        }
                    }));
                }
            }
        }

        private new Rectangle GetTabRect(int index)
        {
            if (index < 0) return default(Rectangle);
            return base.GetTabRect(index);
        }

        protected override void OnMouseWheel(MouseEventArgs e)
        {
            if (SelectedIndex != -1 && !TabPages[SelectedIndex].Focused)
            {
                foreach (Control control in TabPages[SelectedIndex].Controls)
                    if (control.Focused)
                        return;

                TabPages[SelectedIndex].Select();
                TabPages[SelectedIndex].Focus();
            }

            base.OnMouseWheel(e);
        }

        protected override void OnCreateControl()
        {
            Log.WriteInfo($"[MetroTabControl] OnCreateControl开始 - TabCount:{TabCount}");
            base.OnCreateControl();
            OnFontChanged(EventArgs.Empty);
            FindUpDown();
            Log.WriteInfo($"[MetroTabControl] OnCreateControl结束 - _bUpDown:{_bUpDown}");
        }

        protected override void OnLayout(LayoutEventArgs levent)
        {
            Log.WriteInfo($"[MetroTabControl] OnLayout开始 - _bUpDown:{_bUpDown}, TabCount:{TabCount}");
            base.OnLayout(levent);

            // Layout完成后，UpDown控件应该已经创建
            if (!DesignMode && !_bUpDown)
            {
                Log.WriteInfo($"[MetroTabControl] OnLayout中延迟调用FindUpDown");
                BeginInvoke(new Action(() => {
                    Log.WriteInfo($"[MetroTabControl] OnLayout延迟调用FindUpDown执行");
                    FindUpDown();
                }));
            }
        }

        protected override void OnControlAdded(ControlEventArgs e)
        {
            Log.WriteInfo($"[MetroTabControl] OnControlAdded - TabCount:{TabCount}, _bUpDown:{_bUpDown}");
            base.OnControlAdded(e);
            FindUpDown();
            UpdateUpDown();
        }

        protected override void OnControlRemoved(ControlEventArgs e)
        {
            Log.WriteInfo($"[MetroTabControl] OnControlRemoved - TabCount:{TabCount}, _bUpDown:{_bUpDown}");
            base.OnControlRemoved(e);
            FindUpDown();
            UpdateUpDown();
        }

        protected override void OnSelectedIndexChanged(EventArgs e)
        {
            base.OnSelectedIndexChanged(e);
            UpdateUpDown();
            Invalidate();
        }

        [DllImport("user32.dll")]
        private static extern IntPtr SendMessage(IntPtr hWnd, int msg, IntPtr wParam, IntPtr lParam);

        [SecuritySafeCritical]
        protected override void OnFontChanged(EventArgs e)
        {
            base.OnFontChanged(e);
            var wParam = MetroFonts.TabControl(FontSize, FontWeight).ToHfont();
            SendMessage(Handle, 48, wParam, (IntPtr)(-1));
            SendMessage(Handle, 29, IntPtr.Zero, IntPtr.Zero);
            UpdateStyles();
        }

        private void MetroTabControl_Selecting(object sender, TabControlCancelEventArgs e)
        {
            if (_tabDisable.Count > 0 && _tabDisable.Contains(e.TabPage.Name)) e.Cancel = true;
        }

        [SecuritySafeCritical]
        private void FindUpDown()
        {
            if (DesignMode) return;

            try
            {
                Log.WriteInfo($"[MetroTabControl] FindUpDown开始 - TabCount:{TabCount}, _bUpDown:{_bUpDown}");

                var flag = false;
                var window = WinApi.GetWindow(Handle, 5);
                var childCount = 0;

                while (window != IntPtr.Zero)
                {
                    childCount++;
                    var array = new char[33];
                    var className = WinApi.GetClassName(window, array, 32);
                    var a = new string(array, 0, className);

                    Log.WriteInfo($"[MetroTabControl] 找到子窗口{childCount}: {a}, Handle:{window}");

                    if (a == "msctls_updown32")
                    {
                        flag = true;
                        Log.WriteInfo($"[MetroTabControl] 找到UpDown控件! Handle:{window}, 当前_bUpDown:{_bUpDown}");

                        if (!_bUpDown)
                        {
                            try
                            {
                                Log.WriteInfo($"[MetroTabControl] 开始子类化UpDown控件...");
                                _scUpDown = new SubClass(window, true);
                                _scUpDown.SubClassedWndProc += scUpDown_SubClassedWndProc;
                                _bUpDown = true;

                                Log.WriteInfo($"[MetroTabControl] 子类化成功! 开始UpdateUpDown...");
                                // 立即触发一次重绘以应用自定义样式
                                UpdateUpDown();
                                Log.WriteInfo($"[MetroTabControl] UpdateUpDown完成");
                            }
                            catch (Exception ex)
                            {
                                Log.WriteInfo($"[MetroTabControl] 子类化失败: {ex.Message}");
                                // 子类化失败，保持原状态
                                _bUpDown = false;
                                _scUpDown = null;
                            }
                        }
                        else
                        {
                            Log.WriteInfo($"[MetroTabControl] UpDown控件已经子类化过了");
                        }
                        break;
                    }

                    window = WinApi.GetWindow(window, 2);
                }

                if (!flag && _bUpDown)
                {
                    Log.WriteInfo($"[MetroTabControl] 未找到UpDown控件，重置状态");
                    _bUpDown = false;
                    _scUpDown = null;
                }

                Log.WriteInfo($"[MetroTabControl] FindUpDown结束 - 找到UpDown:{flag}, 子窗口总数:{childCount}, 最终_bUpDown:{_bUpDown}");
            }
            catch (Exception ex)
            {
                Log.WriteInfo($"[MetroTabControl] FindUpDown异常: {ex.Message}");
            }
        }

        [SecuritySafeCritical]
        private void UpdateUpDown()
        {
            try
            {
                Log.WriteInfo($"[MetroTabControl] UpdateUpDown开始 - _bUpDown:{_bUpDown}, DesignMode:{DesignMode}");

                if (_bUpDown && !DesignMode && _scUpDown != null)
                {
                    var isVisible = WinApi.IsWindowVisible(_scUpDown.Handle);
                    Log.WriteInfo($"[MetroTabControl] UpDown控件可见性:{isVisible}, Handle:{_scUpDown.Handle}");

                    if (isVisible)
                    {
                        var rect = default(Rectangle);
                        WinApi.GetClientRect(_scUpDown.Handle, ref rect);
                        WinApi.InvalidateRect(_scUpDown.Handle, ref rect, true);
                        Log.WriteInfo($"[MetroTabControl] 已触发UpDown控件重绘, Rect:{rect}");
                    }
                    else
                    {
                        Log.WriteInfo($"[MetroTabControl] UpDown控件不可见，跳过重绘");
                    }
                }
                else
                {
                    Log.WriteInfo($"[MetroTabControl] UpdateUpDown条件不满足 - _bUpDown:{_bUpDown}, _scUpDown:{(_scUpDown != null ? "不为null" : "为null")}");
                }
            }
            catch (Exception ex)
            {
                Log.WriteInfo($"[MetroTabControl] UpdateUpDown异常: {ex.Message}");
            }
        }

        [SecuritySafeCritical]
        private int scUpDown_SubClassedWndProc(ref Message m)
        {
            var msg = m.Msg;
            if (msg == 15) // WM_PAINT
            {
                try
                {
                    Log.WriteInfo($"[MetroTabControl] 收到UpDown控件WM_PAINT消息，开始自定义绘制");
                    var windowDc = WinApi.GetWindowDC(_scUpDown.Handle);
                    var graphics = Graphics.FromHdc(windowDc);
                    DrawUpDown(graphics);
                    graphics.Dispose();
                    WinApi.ReleaseDC(_scUpDown.Handle, windowDc);
                    m.Result = IntPtr.Zero;
                    var rect = default(Rectangle);
                    WinApi.GetClientRect(_scUpDown.Handle, ref rect);
                    WinApi.ValidateRect(_scUpDown.Handle, ref rect);
                    Log.WriteInfo($"[MetroTabControl] UpDown控件自定义绘制完成");
                    return 1;
                }
                catch (Exception ex)
                {
                    Log.WriteInfo($"[MetroTabControl] UpDown控件绘制异常: {ex.Message}");
                    return 0; // 让系统处理
                }
            }

            return 0;
        }
    }

    public class HiddenTabs
    {
        public HiddenTabs(int id, string page)
        {
            Index = id;
            TabPage = page;
        }

        public int Index { get; }

        public string TabPage { get; }
    }

    [SuppressUnmanagedCodeSecurity]
    public class SubClass : NativeWindow
    {
        public delegate int SubClassWndProcEventHandler(ref Message m);

        public SubClass(IntPtr handle, bool subClass)
        {
            AssignHandle(handle);
            SubClassed = subClass;
        }

        public bool SubClassed { get; set; }

        public event SubClassWndProcEventHandler SubClassedWndProc;

        protected override void WndProc(ref Message m)
        {
            if (!SubClassed || OnSubClassedWndProc(ref m) == 0) base.WndProc(ref m);
        }

        public void CallDefaultWndProc(ref Message m)
        {
            base.WndProc(ref m);
        }

        public int HiWord(int number)
        {
            return (number >> 16) & 0xFFFF;
        }

        public int LoWord(int number)
        {
            return number & 0xFFFF;
        }

        public int MakeLong(int loWord, int hiWord)
        {
            return (hiWord << 16) | (loWord & 0xFFFF);
        }

        public IntPtr MakeLParam(int loWord, int hiWord)
        {
            return (IntPtr)((hiWord << 16) | (loWord & 0xFFFF));
        }

        private int OnSubClassedWndProc(ref Message m)
        {
            if (SubClassedWndProc != null) return SubClassedWndProc(ref m);
            return 0;
        }
    }

    public class MetroPaintEventArgs : EventArgs
    {
        public MetroPaintEventArgs(Color backColor, Color foreColor, Graphics g)
        {
            BackColor = backColor;
            ForeColor = foreColor;
            Graphics = g;
        }

        public Color BackColor { get; }

        public Color ForeColor { get; }

        public Graphics Graphics { get; }
    }
}